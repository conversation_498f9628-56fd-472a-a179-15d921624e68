import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref, computed } from 'vue'
import { nextTick } from 'vue'
import RealtyGamePagesLayout from '../layouts/RealtyGamePagesLayout.vue'

// Mock the router and route
let mockRoute = {
  name: 'rPriceGameProperty',
  params: {
    gameSlug: 'test-game',
    propertyUuid: 'test-property-uuid',
    listingInGameUuid: 'test-listing-uuid'
  },
  query: {
    session: 'test-session-id'
  }
}

const mockRouter = {
  push: vi.fn(),
  resolve: vi.fn(() => ({ href: '/test-path' }))
}

// Mock composables
vi.mock('vue-router', () => ({
  useRoute: () => mockRoute,
  useRouter: () => mockRouter
}))

// Mock composables with comprehensive functionality
const mockSingleListingRealtyGame = {
  totalProperties: ref(5),
  gameTitle: ref('Test Realty Game'),
  realtyGameSummary: ref({ total_listings: 5 }),
  gameCommunitiesDetails: ref({}),
  firstPropListing: ref({}),
  fetchPriceGuessData: vi.fn(),
  gameDefaultCurrency: ref('GBP'),
  getPropertyByUuid: vi.fn(),
  fetchPropertyByUuid: vi.fn()
}

const mockRealtyGameStorage = {
  getCurrentSessionId: vi.fn(() => 'current-session-id'),
  getCurrencySelection: vi.fn(() => 'USD'),
  initializeFromStorage: vi.fn()
}

const mockServerSingleListingGameResults = {
  isLoading: ref(false),
  error: ref(null),
  results: ref(null),
  playerResults: ref(null),
  comparisonSummary: ref(null),
  gameBreakdown: ref(null),
  ssGameSession: ref(null),
  fetchResults: vi.fn(),
  getScoreColor: vi.fn(),
  realtyGameListingDetails: ref({})
}

const mockCurrencyConverter = {
  setCurrency: vi.fn(),
  formatPriceWithBothCurrencies: vi.fn(() => '£500,000 ($625,000)')
}

vi.mock('src/concerns/realty-game/composables/useSingleListingRealtyGame', () => ({
  useSingleListingRealtyGame: () => mockSingleListingRealtyGame
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: () => mockRealtyGameStorage
}))

vi.mock('src/concerns/realty-game/composables/useServerSingleListingGameResults', () => ({
  useServerSingleListingGameResults: () => mockServerSingleListingGameResults
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: () => mockCurrencyConverter
}))

describe('RealtyGamePagesLayout', () => {
  let wrapper

  const mockPropertyData = {
    uuid: 'test-property-uuid',
    listing_details: {
      gl_title_atr: 'Beautiful Test Property',
      street_address: '123 Test Street',
      city: 'Test City',
      postal_code: 'TEST123',
      region: 'Test Region',
      title: 'Beautiful Test Property',
      sale_listing_pics: [
        {
          flag_is_hidden: false,
          image_details: {
            url: 'https://example.com/image1.jpg'
          }
        },
        {
          flag_is_hidden: true,
          image_details: {
            url: 'https://example.com/image2.jpg'
          }
        }
      ]
    }
  }

  const createWrapper = (routeOverrides = {}, options = {}) => {
    // Update mock route with overrides
    Object.assign(mockRoute, routeOverrides)

    return mount(RealtyGamePagesLayout, {
      global: {
        stubs: {
          'router-view': {
            template: '<div class="router-view-stub">Router View Content</div>'
          },
          'q-icon': {
            template: '<div class="q-icon-stub">{{ name }}</div>',
            props: ['name', 'size', 'color']
          }
        }
      },
      ...options
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset mock route to default state
    Object.assign(mockRoute, {
      name: 'rPriceGameProperty',
      params: {
        gameSlug: 'test-game',
        propertyUuid: 'test-property-uuid',
        listingInGameUuid: 'test-listing-uuid'
      },
      query: {
        session: 'test-session-id'
      }
    })

    // Reset mock implementations
    mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)
    mockSingleListingRealtyGame.fetchPropertyByUuid.mockResolvedValue(mockPropertyData)
    mockRealtyGameStorage.getCurrentSessionId.mockReturnValue('current-session-id')
    mockRealtyGameStorage.getCurrencySelection.mockReturnValue('USD')
    mockServerSingleListingGameResults.realtyGameListingDetails.value = {}
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Component Mounting and Basic Functionality', () => {
    it('should mount successfully', () => {
      wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.realty-game-pages-layout').exists()).toBe(true)
    })

    it('should render router-view with correct props', () => {
      wrapper = createWrapper()
      const routerView = wrapper.find('.router-view-stub')
      expect(routerView.exists()).toBe(true)
    })

    it('should initialize game data on mount', async () => {
      wrapper = createWrapper()
      await nextTick()
      
      expect(mockSingleListingRealtyGame.fetchPriceGuessData).toHaveBeenCalledWith('test-game')
      expect(mockRealtyGameStorage.initializeFromStorage).toHaveBeenCalled()
      expect(mockCurrencyConverter.setCurrency).toHaveBeenCalled()
    })
  })

  describe('Property Header Display', () => {
    it('should show property header when property data is available', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper()
      await nextTick()

      // Wait for component to fully render
      await wrapper.vm.$nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      expect(header.exists()).toBe(true)

      const title = wrapper.find('[data-testid="property-title"]')
      expect(title.text()).toBe('Beautiful Test Property')

      const address = wrapper.find('[data-testid="property-address"]')
      expect(address.text()).toBe('123 Test Street')

      const gameTitle = wrapper.find('[data-testid="game-title"]')
      expect(gameTitle.text()).toBe('Part of Test Realty Game')
    })

    it('should hide property header when no property UUID', () => {
      wrapper = createWrapper({
        params: { gameSlug: 'test-game' }
      })
      
      const header = wrapper.find('[data-testid="property-header"]')
      expect(header.exists()).toBe(false)
    })

    it('should display property location when no street address', async () => {
      const propertyWithoutAddress = {
        ...mockPropertyData,
        listing_details: {
          ...mockPropertyData.listing_details,
          street_address: null
        }
      }
      
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(propertyWithoutAddress)
      
      wrapper = createWrapper()
      await nextTick()
      
      const location = wrapper.find('[data-testid="property-location"]')
      expect(location.text()).toContain('Test City')
      expect(location.text()).toContain('TEST123')
    })

    it('should display property thumbnail when images are available', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)
      
      wrapper = createWrapper()
      await nextTick()
      
      const thumbnail = wrapper.find('[data-testid="property-thumbnail"]')
      expect(thumbnail.exists()).toBe(true)
      
      const img = thumbnail.find('img')
      expect(img.attributes('src')).toBe('https://example.com/image1.jpg')
    })

    it('should show placeholder when no images available', async () => {
      const propertyWithoutImages = {
        ...mockPropertyData,
        listing_details: {
          ...mockPropertyData.listing_details,
          sale_listing_pics: []
        }
      }
      
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(propertyWithoutImages)
      
      wrapper = createWrapper()
      await nextTick()
      
      const placeholder = wrapper.find('[data-testid="property-placeholder"]')
      expect(placeholder.exists()).toBe(true)
    })

    it('should filter out hidden images', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)
      
      wrapper = createWrapper()
      await nextTick()
      
      // Should only show the first image (not hidden)
      const thumbnail = wrapper.find('[data-testid="property-thumbnail"]')
      expect(thumbnail.exists()).toBe(true)
      
      const img = thumbnail.find('img')
      expect(img.attributes('src')).toBe('https://example.com/image1.jpg')
    })
  })

  describe('Data Fetching Strategy', () => {
    it('should use property from game listings when available (most efficient)', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper()
      await nextTick()

      // Should not call fetchPropertyByUuid since data is already available
      expect(mockSingleListingRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
      expect(mockSingleListingRealtyGame.getPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })

    it('should fetch individual property data when not in game listings', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)

      wrapper = createWrapper()
      await nextTick()

      expect(mockSingleListingRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })

    it('should handle property fetch errors gracefully', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)
      mockSingleListingRealtyGame.fetchPropertyByUuid.mockRejectedValue(new Error('Network error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      wrapper = createWrapper()
      await nextTick()

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching property data:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should refetch property data when property UUID changes', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)

      wrapper = createWrapper()
      await nextTick()

      expect(mockSingleListingRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')

      // Change property UUID
      mockRoute.params.propertyUuid = 'new-property-uuid'
      await wrapper.vm.$forceUpdate()
      await nextTick()

      expect(mockSingleListingRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('new-property-uuid')
    })

    it('should use results data as fallback when available', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)
      mockServerSingleListingGameResults.realtyGameListingDetails.value = {
        gl_title_atr: 'Results Property',
        street_address: '456 Results Street'
      }

      wrapper = createWrapper()
      await nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      expect(header.exists()).toBe(true)
    })
  })

  describe('Session and Currency Management', () => {
    it('should initialize currency from session storage', async () => {
      mockRealtyGameStorage.getCurrencySelection.mockReturnValue('EUR')

      wrapper = createWrapper()
      await nextTick()

      expect(mockCurrencyConverter.setCurrency).toHaveBeenCalledWith('EUR')
    })

    it('should fallback to game default currency when no session currency', async () => {
      mockRealtyGameStorage.getCurrencySelection.mockReturnValue(null)
      mockSingleListingRealtyGame.gameDefaultCurrency.value = 'USD'

      wrapper = createWrapper()
      await nextTick()

      expect(mockCurrencyConverter.setCurrency).toHaveBeenCalledWith('USD')
    })

    it('should compute correct session IDs', () => {
      wrapper = createWrapper()

      // Should use session from query params
      expect(wrapper.vm.routeSssnId).toBe('test-session-id')
    })

    it('should determine if current user session correctly', () => {
      mockRealtyGameStorage.getCurrentSessionId.mockReturnValue('test-session-id')

      wrapper = createWrapper()

      expect(wrapper.vm.isCurrentUserSession).toBe(true)
    })
  })

  describe('Navigation and Event Handling', () => {
    it('should handle header click navigation', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper({
        name: 'rPriceGameResults' // Different route to make header clickable
      })
      await nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      await header.trigger('click')

      expect(mockRouter.push).toHaveBeenCalledWith({
        name: 'rPriceGameProperty',
        params: {
          gameSlug: 'test-game',
          listingInGameUuid: 'test-property-uuid'
        }
      })
    })

    it('should not navigate when already on property page', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper({
        name: 'rPriceGameProperty'
      })
      await nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      await header.trigger('click')

      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should handle load results event', async () => {
      wrapper = createWrapper()

      await wrapper.vm.handleLoadResults()

      expect(mockServerSingleListingGameResults.fetchResults).toHaveBeenCalledWith(
        'test-session-id',
        'test-property-uuid'
      )
    })

    it('should handle game complete event', async () => {
      wrapper = createWrapper()

      wrapper.vm.handleGameComplete('completed-session-id')

      expect(mockRouter.push).toHaveBeenCalledWith({
        name: 'rPriceGameResults',
        params: {
          gameSlug: 'test-game',
          routeSssnId: 'completed-session-id'
        }
      })
    })

    it('should compute shareable results URL correctly', () => {
      wrapper = createWrapper()

      const expectedUrl = `${location.origin}/test-path`
      expect(wrapper.vm.shareableResultsUrl).toBe(expectedUrl)

      expect(mockRouter.resolve).toHaveBeenCalledWith({
        name: 'rPriceGameResultsShareable',
        params: {
          gameSlug: 'test-game',
          routeSssnId: 'test-session-id'
        }
      })
    })
  })

  describe('Responsive Design and Styling', () => {
    it('should apply clickable class when not on property page', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper({
        name: 'rPriceGameResults'
      })
      await nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      expect(header.classes()).toContain('clickable')
    })

    it('should not apply clickable class when on property page', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper({
        name: 'rPriceGameProperty'
      })
      await nextTick()

      const header = wrapper.find('[data-testid="property-header"]')
      expect(header.classes()).not.toContain('clickable')
    })

    it('should have mobile-hide class on thumbnail', async () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)

      wrapper = createWrapper()
      await nextTick()

      const thumbnailContainer = wrapper.find('.header-thumbnail')
      expect(thumbnailContainer.classes()).toContain('mobile-hide')
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing game slug gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      wrapper = createWrapper({
        params: {}
      })
      await nextTick()

      // Should not crash
      expect(wrapper.exists()).toBe(true)

      consoleSpy.mockRestore()
    })

    it('should handle missing property UUID gracefully', () => {
      wrapper = createWrapper({
        params: { gameSlug: 'test-game' }
      })

      expect(wrapper.vm.showPropertyHeader).toBe(false)
      expect(wrapper.find('[data-testid="property-header"]').exists()).toBe(false)
    })

    it('should warn when no current session ID found', () => {
      mockRealtyGameStorage.getCurrentSessionId.mockReturnValue(null)
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      wrapper = createWrapper()

      expect(wrapper.vm.currPlayerSssnId).toBe(null)

      consoleSpy.mockRestore()
    })

    it('should handle game data fetch errors', async () => {
      mockSingleListingRealtyGame.fetchPriceGuessData.mockRejectedValue(new Error('Game fetch error'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      wrapper = createWrapper()
      await nextTick()

      expect(consoleSpy).toHaveBeenCalledWith('Error fetching game data:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })
})
