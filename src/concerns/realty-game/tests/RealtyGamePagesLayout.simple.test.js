import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import RealtyGamePagesLayout from '../layouts/RealtyGamePagesLayout.vue'

// Mock the router and route
const mockRoute = {
  name: 'rPriceGameProperty',
  params: {
    gameSlug: 'test-game',
    propertyUuid: 'test-property-uuid'
  },
  query: {
    session: 'test-session-id'
  }
}

const mockRouter = {
  push: vi.fn(),
  resolve: vi.fn(() => ({ href: '/test-path' }))
}

// Mock composables
vi.mock('vue-router', () => ({
  useRoute: () => mockRoute,
  useRouter: () => mockRouter
}))

// Mock composables with minimal functionality
const mockSingleListingRealtyGame = {
  totalProperties: ref(5),
  gameTitle: ref('Test Realty Game'),
  realtyGameSummary: ref({}),
  gameCommunitiesDetails: ref({}),
  firstPropListing: ref({}),
  fetchPriceGuessData: vi.fn().mockResolvedValue({}),
  gameDefaultCurrency: ref('GBP'),
  getPropertyByUuid: vi.fn(),
  fetchPropertyByUuid: vi.fn().mockResolvedValue({})
}

const mockRealtyGameStorage = {
  getCurrentSessionId: vi.fn(() => 'current-session-id'),
  getCurrencySelection: vi.fn(() => 'USD'),
  initializeFromStorage: vi.fn()
}

const mockServerSingleListingGameResults = {
  isLoading: ref(false),
  error: ref(null),
  results: ref(null),
  playerResults: ref(null),
  comparisonSummary: ref(null),
  gameBreakdown: ref(null),
  ssGameSession: ref(null),
  fetchResults: vi.fn(),
  getScoreColor: vi.fn(),
  realtyGameListingDetails: ref({})
}

const mockCurrencyConverter = {
  setCurrency: vi.fn(),
  formatPriceWithBothCurrencies: vi.fn(() => '£500,000')
}

vi.mock('src/concerns/realty-game/composables/useSingleListingRealtyGame', () => ({
  useSingleListingRealtyGame: () => mockSingleListingRealtyGame
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: () => mockRealtyGameStorage
}))

vi.mock('src/concerns/realty-game/composables/useServerSingleListingGameResults', () => ({
  useServerSingleListingGameResults: () => mockServerSingleListingGameResults
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: () => mockCurrencyConverter
}))

describe('RealtyGamePagesLayout', () => {
  let wrapper

  const mockPropertyData = {
    uuid: 'test-property-uuid',
    listing_details: {
      gl_title_atr: 'Beautiful Test Property',
      street_address: '123 Test Street',
      city: 'Test City',
      postal_code: 'TEST123',
      sale_listing_pics: [
        {
          flag_is_hidden: false,
          image_details: {
            url: 'https://example.com/image1.jpg'
          }
        }
      ]
    }
  }

  const createWrapper = () => {
    return mount(RealtyGamePagesLayout, {
      global: {
        stubs: {
          'router-view': {
            template: '<div class="router-view-stub">Router View Content</div>'
          },
          'QIcon': {
            template: '<div class="q-icon-stub">{{ name }}</div>',
            props: ['name', 'size', 'color']
          }
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(null)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Component Mounting', () => {
    it('should mount successfully', () => {
      wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.realty-game-pages-layout').exists()).toBe(true)
    })

    it('should render router-view', () => {
      wrapper = createWrapper()
      const routerView = wrapper.find('.router-view-stub')
      expect(routerView.exists()).toBe(true)
    })

    it('should initialize game data on mount', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      expect(mockSingleListingRealtyGame.fetchPriceGuessData).toHaveBeenCalledWith('test-game')
      expect(mockRealtyGameStorage.initializeFromStorage).toHaveBeenCalled()
    })
  })

  describe('Data Fetching Optimization', () => {
    it('should use cached property data when available', () => {
      mockSingleListingRealtyGame.getPropertyByUuid.mockReturnValue(mockPropertyData)
      
      wrapper = createWrapper()
      
      // Should use cached data, not fetch
      expect(mockSingleListingRealtyGame.getPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })

    it('should have correct computed properties', () => {
      wrapper = createWrapper()
      
      // Test computed properties
      expect(wrapper.vm.routeSssnId).toBe('test-session-id')
      expect(wrapper.vm.propertyUuid).toBe('test-property-uuid')
      expect(wrapper.vm.showPropertyHeader).toBe(true)
    })

    it('should handle missing property UUID', () => {
      mockRoute.params.propertyUuid = ''
      
      wrapper = createWrapper()
      
      expect(wrapper.vm.showPropertyHeader).toBe(false)
    })
  })

  describe('Session Management', () => {
    it('should initialize currency from session', async () => {
      mockRealtyGameStorage.getCurrencySelection.mockReturnValue('EUR')
      
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      expect(mockCurrencyConverter.setCurrency).toHaveBeenCalledWith('EUR')
    })

    it('should compute session IDs correctly', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.routeSssnId).toBe('test-session-id')
      expect(wrapper.vm.currPlayerSssnId).toBe('current-session-id')
    })

    it('should determine current user session', () => {
      mockRealtyGameStorage.getCurrentSessionId.mockReturnValue('test-session-id')
      
      wrapper = createWrapper()
      
      expect(wrapper.vm.isCurrentUserSession).toBe(true)
    })
  })

  describe('Event Handlers', () => {
    it('should handle game complete event', () => {
      wrapper = createWrapper()
      
      wrapper.vm.handleGameComplete('completed-session-id')
      
      expect(mockRouter.push).toHaveBeenCalledWith({
        name: 'rPriceGameResults',
        params: {
          gameSlug: 'test-game',
          routeSssnId: 'completed-session-id'
        }
      })
    })

    it('should compute shareable results URL', () => {
      wrapper = createWrapper()
      
      const expectedUrl = `${location.origin}/test-path`
      expect(wrapper.vm.shareableResultsUrl).toBe(expectedUrl)
    })
  })

  describe('Error Handling', () => {
    it('should handle missing game slug gracefully', () => {
      mockRoute.params.gameSlug = ''
      
      wrapper = createWrapper()
      
      expect(wrapper.exists()).toBe(true)
    })

    it('should handle fetch errors gracefully', async () => {
      mockSingleListingRealtyGame.fetchPriceGuessData.mockRejectedValue(new Error('Network error'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      expect(wrapper.exists()).toBe(true)
      
      consoleSpy.mockRestore()
    })
  })

  describe('Performance Optimizations', () => {
    it('should use efficient data fetching strategy', () => {
      // Test that the component implements the three-tier strategy
      wrapper = createWrapper()
      
      // Verify computed property exists
      expect(wrapper.vm.currentPropertyListing).toBeDefined()
      
      // Verify property images computed property
      expect(wrapper.vm.propertyImages).toBeDefined()
      expect(Array.isArray(wrapper.vm.propertyImages)).toBe(true)
    })

    it('should have proper watchers for route changes', () => {
      wrapper = createWrapper()

      // Verify the component has the necessary reactive properties
      expect(wrapper.vm.propertyUuid).toBeDefined()
      expect(wrapper.vm.routeSssnId).toBeDefined()
      expect(wrapper.vm.currentPropertyListing).toBeDefined()
    })
  })
})
