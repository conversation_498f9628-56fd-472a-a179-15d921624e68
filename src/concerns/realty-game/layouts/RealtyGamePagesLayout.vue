<!--
/**
 * RealtyGamePagesLayout.vue
 *
 * A layout component that provides a consistent structure for realty game pages.
 * This component serves as a wrapper for all realty game routes and provides:
 *
 * FEATURES:
 * - Property header display with title, address, and thumbnail
 * - Efficient data fetching with multiple fallback strategies
 * - Session management and currency handling
 * - Router-view with comprehensive prop passing
 * - Responsive design with mobile optimizations
 *
 * DATA FETCHING STRATEGY:
 * 1. First checks if property data exists in already-loaded game listings (most efficient)
 * 2. Falls back to individually fetched property data if needed
 * 3. Uses results data as final fallback for results pages
 * 4. Only makes API calls when data is not available from existing sources
 *
 * PROPS PASSED TO CHILD ROUTES:
 * - Session and routing data (currPlayerSssnId, gameSessionId, routeSssnId)
 * - Property data (propertyUuid, currentPropertyListing, isPropertyLoading)
 * - Game data (gameTitle, gameDefaultCurrency, totalProperties, gameCommunitiesDetails)
 * - Results data (results, playerResults, comparisonSummary, gameBreakdown)
 * - Utility functions (formatPriceWithBothCurrencies, getScoreColor)
 *
 * EVENTS HANDLED:
 * - @load-results: Triggers results fetching for the current property/session
 * - @update-progress: Handles progress updates from child components
 * - @game-complete: Navigates to results page when game is completed
 *
 * RESPONSIVE BEHAVIOR:
 * - Property thumbnail hidden on mobile devices
 * - Header becomes clickable on non-property routes
 * - Hover effects for interactive elements
 *
 * PERFORMANCE OPTIMIZATIONS:
 * - Computed properties for reactive data
 * - Efficient property data fetching with caching
 * - Watchers for route changes to minimize unnecessary API calls
 * - Lazy loading of property data only when needed
 */
-->

<template>
  <div class="realty-game-pages-layout">
    <!-- Property Header for all property-specific routes -->
    <div
      v-if="showPropertyHeader && currentPropertyListing"
      class="layout-rgpage-prop-header q-mb-lg q-px-sm"
      :class="{ clickable: $route.name !== 'rPriceGameProperty' }"
      @click="handleHeaderClick"
      data-testid="property-header"
    >
      <div class="row items-center justify-between">
        <div class="header-info col-auto q-pa-md">
          <h1 class="text-h4 text-weight-bold text-primary q-my-xs" data-testid="property-title">
            {{ currentPropertyListing.realty_game_listing.gl_title_atr }}
          </h1>
          <p
            class="text-body1 text-grey-7"
            v-if="currentPropertyListing.sale_listing.street_address"
            data-testid="property-address"
          >
            {{ currentPropertyListing.sale_listing.street_address }}
          </p>
          <p class="text-body1 text-grey-7" v-else data-testid="property-location">
            {{ currentPropertyListing.sale_listing.city }},
            {{ currentPropertyListing.sale_listing.postal_code || currentPropertyListing.sale_listing.region }}
          </p>
          <div class="text-caption text-grey-6 q-mt-xs" v-if="gameTitle" data-testid="game-title">
            Part of {{ gameTitle }}
          </div>
        </div>

        <div class="header-thumbnail col-auto mobile-hide">
          <div class="thumbnail-container" v-if="propertyImages.length > 0" data-testid="property-thumbnail">
            <img
              :src="propertyImages[0].image_details.url"
              :alt="currentPropertyListing.sale_listing.title"
              class="property-thumbnail"
            />
            <div class="thumbnail-overlay">
              <!-- <q-icon name="photo_camera" size="sm" color="white" />
              <span class="text-caption text-white q-ml-xs">
                {{ propertyImages.length }}
              </span> -->
            </div>
          </div>
          <div class="thumbnail-placeholder" v-else data-testid="property-placeholder">
            <q-icon name="home" size="lg" color="grey-5" />
          </div>
        </div>
      </div>
    </div>
    <router-view
      :curr-player-sssn-id="currPlayerSssnId"
      :game-session-id="routeSssnId"
      :route-sssn-id="routeSssnId"
      :property-uuid="propertyUuid"
      :game-communities-details="gameCommunitiesDetails"
      :shareable-results-url="shareableResultsUrl"
      :is-current-user-session="isCurrentUserSession"
      :game-title="gameTitle"
      :game-default-currency="gameDefaultCurrency"
      :total-properties="totalProperties"
      :first-prop-listing="firstPropListing"
      :realty-game-summary="realtyGameSummary"
      :is-loading="isLoading"
      :error="error"
      :results="results"
      :player-results="playerResults"
      :ss-game-session="ssGameSession"
      :comparison-summary="comparisonSummary"
      :game-breakdown="gameBreakdown"
      :overall-ranking="overallRanking"
      :leaderboard="leaderboard"
      :show-leaderboard="showLeaderboard"
      :get-score-color="getScoreColor"
      :format-price-with-both-currencies="formatPriceWithBothCurrencies"
      :current-property-listing="currentPropertyListing"
      :is-property-loading="isPropertyLoading"
      :property-error="propertyError"
      @load-results="handleLoadResults"
      @update-progress="handleProgressUpdate"
      @game-complete="handleGameComplete"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSingleListingRealtyGame } from 'src/concerns/realty-game/composables/useSingleListingRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import { useServerSingleListingGameResults } from 'src/concerns/realty-game/composables/useServerSingleListingGameResults'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'

const $route = useRoute()
const $router = useRouter()

// Initialize composables
const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDefaultCurrency,
  getPropertyByUuid,
  fetchPropertyByUuid,
} = useSingleListingRealtyGame()

const { getCurrentSessionId, getCurrencySelection, initializeFromStorage } = useRealtyGameStorage()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
  realtyGameListingDetails,
} = useServerSingleListingGameResults()

const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Property data state - using more efficient approach
const currentPropertyData = ref(null)
const isPropertyLoading = ref(false)
const propertyError = ref(null)

// Computed properties
const routeSssnId = computed(
  () => $route.query.session || $route.params.routeSssnId || getCurrentSessionId() || ''
)

const propertyUuid = computed(() => $route.params.propertyUuid || $route.params.listingInGameUuid || '')

// Show property header for all routes that have propertyUuid or listingInGameUuid parameter
const showPropertyHeader = computed(() => {
  return !!propertyUuid.value
})

// Combined property data that merges game listing data with sale_listing data
const currentPropertyListing = computed(() => {
  // First try to get property from already loaded game listings (most efficient)
  if (propertyUuid.value) {
    const gameProperty = getPropertyByUuid(propertyUuid.value)
    if (gameProperty) {
      return {
        realty_game_listing: gameProperty.listing_details || {},
        sale_listing: gameProperty.listing_details || {}
      }
    }
  }

  // Fallback to fetched property data if available
  if (currentPropertyData.value) {
    const gameListingData = currentPropertyData.value.realty_game_listing || {}
    const saleListingData = currentPropertyData.value.sale_listing || currentPropertyData.value

    return {
      realty_game_listing: gameListingData,
      sale_listing: saleListingData
    }
  }

  // Fallback to results data if available (for results pages)
  if (realtyGameListingDetails.value && Object.keys(realtyGameListingDetails.value).length > 0) {
    return {
      realty_game_listing: realtyGameListingDetails.value,
      sale_listing: realtyGameListingDetails.value
    }
  }

  return null
})

// Property images computed property
const propertyImages = computed(() => {
  if (!currentPropertyListing.value?.sale_listing?.sale_listing_pics) return []
  return currentPropertyListing.value.sale_listing.sale_listing_pics.filter(
    (image) => !image.flag_is_hidden
  )
})

const shareableResultsUrl = computed(() => {
  if (!routeSssnId.value || !$route.params.gameSlug) {
    return ''
  }
  let shareRoute = {
    name: 'rPriceGameResultsShareable',
    params: {
      gameSlug: $route.params.gameSlug,
      routeSssnId: routeSssnId.value,
    },
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  return fullPath
})

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === routeSssnId.value
})

const currPlayerSssnId = computed(() => {
  // Try to get current session ID, create one if it doesn't exist
  const currentId = getCurrentSessionId()
  if (!currentId) {
    console.warn('No current session ID found in layout, this may cause issues')
    // Don't create here as it might cause reactivity issues
    // Let the child component handle creation when needed
  }
  return currentId
})

// Results-specific computed properties
const overallRanking = computed(() => results.value?.overall_ranking || null)
const leaderboard = computed(() => results.value?.leaderboard || [])
const showLeaderboard = computed(() => {
  return leaderboard.value && leaderboard.value.length > 1
})

// Event handlers
const handleLoadResults = async () => {
  const propertyId = $route.params.propertyUuid || $route.params.listingInGameUuid
  if (routeSssnId.value && propertyId) {
    await fetchResults(routeSssnId.value, propertyId)
  }
}

const handleProgressUpdate = (data) => {
  console.log('Progress update:', data)
}

const handleGameComplete = (sessionId) => {
  $router.push({
    name: 'rPriceGameResults',
    params: {
      gameSlug: $route.params.gameSlug,
      routeSssnId: sessionId,
    },
  })
}

// New click handler for property header
const handleHeaderClick = () => {
  if ($route.name !== 'rPriceGameProperty') {
    $router.push({
      name: 'rPriceGameProperty',
      params: {
        gameSlug: $route.params.gameSlug,
        listingInGameUuid: propertyUuid.value,
      },
    })
  }
}

// Initialize currency from session
const initializeCurrency = () => {
  const sessionCurrency = getCurrencySelection(routeSssnId.value)
  if (sessionCurrency) {
    setCurrency(sessionCurrency)
  } else if (gameDefaultCurrency.value) {
    setCurrency(gameDefaultCurrency.value)
  }
}

// Fetch property data only when not available in game listings
const fetchPropertyDataIfNeeded = async () => {
  if (!propertyUuid.value) return

  // Check if we already have the property in game listings
  const gameProperty = getPropertyByUuid(propertyUuid.value)
  if (gameProperty) {
    // Property is already available in game listings, no need to fetch
    return
  }

  // Only fetch if not available in game listings
  isPropertyLoading.value = true
  propertyError.value = null

  try {
    const response = await fetchPropertyByUuid(propertyUuid.value)
    currentPropertyData.value = response
  } catch (error) {
    console.error('Error fetching property data:', error)
    propertyError.value = 'Failed to load property data'
  } finally {
    isPropertyLoading.value = false
  }
}

// Watch for route changes to fetch appropriate data
watch(
  () => [$route.params.gameSlug, propertyUuid.value],
  async () => {
    if (propertyUuid.value) {
      await fetchPropertyDataIfNeeded()
    }
  },
  { immediate: true }
)

// Watch for game slug changes to fetch game data
watch(
  () => $route.params.gameSlug,
  async (newGameSlug) => {
    if (newGameSlug) {
      try {
        await fetchPriceGuessData(newGameSlug)
        initializeFromStorage()
        initializeCurrency()
      } catch (error) {
        console.error('Error fetching game data:', error)
      }
    }
  },
  { immediate: true }
)

// Initialize on mount
onMounted(async () => {
  if ($route.params.gameSlug) {
    try {
      await fetchPriceGuessData($route.params.gameSlug)
      initializeFromStorage()
      initializeCurrency()
    } catch (error) {
      console.error('Error initializing game data:', error)
    }
  }
})
</script>

<style lang="scss" scoped>
.realty-game-pages-layout {
  min-height: 100vh;
}

.layout-rgpage-prop-header {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;

  &.clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
  }

  .header-info {
    flex: 1;
    min-width: 0;

    h1 {
      margin: 0;
      word-wrap: break-word;
    }

    p {
      margin: 4px 0;
      word-wrap: break-word;
    }
  }

  .header-thumbnail {
    flex-shrink: 0;
    margin-left: 16px;

    @media (max-width: 600px) {
      display: none;
    }
  }

  .thumbnail-container {
    position: relative;
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;

    .property-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .thumbnail-overlay {
      position: absolute;
      top: 4px;
      right: 4px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 12px;
      padding: 2px 6px;
      display: flex;
      align-items: center;
    }
  }

  .thumbnail-placeholder {
    width: 120px;
    height: 80px;
    background: #f5f5f5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
  }
}

.mobile-hide {
  @media (max-width: 600px) {
    display: none !important;
  }
}
</style>
